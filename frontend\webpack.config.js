const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

// Load environment variables from .env files
require('dotenv').config({ path: '.env.development' });
require('dotenv').config({ path: '.env.local' });
require('dotenv').config({ path: '.env' });

// Note: Proxy configuration is handled by setupProxy.js

// Environment variables
const isProduction = process.env.NODE_ENV === 'production';

module.exports = {
  // Entry point for the application - using minimal version for bundle size optimization
  entry: {
    main: ['./src/minimal-index.js']
  },

  // Output configuration
  output: {
    path: path.resolve(__dirname, 'build'),
    publicPath: '/',
    filename: 'static/js/[name].[contenthash:8].js',
    chunkFilename: 'static/js/[name].[contenthash:8].chunk.js',
    // Removed chunkFormat: 'array-push' to fix build error
  },

  // Module rules for different file types
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']
          }
        }
      },
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader']
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'static/media/[name].[hash:8][ext]'
        }
      }
    ]
  },

  // Development server configuration
  devServer: {
    host: '0.0.0.0',
    port: 3000,
    historyApiFallback: {
      index: '/index.html',
      disableDotRule: true,
      rewrites: [
        {
          from: /^\/api\/.*$/, to: function (context) {
            return context.parsedUrl.pathname;
          }
        },
        { from: /./, to: '/index.html' }
      ]
    },
    static: {
      directory: path.join(__dirname, 'public'),
      watch: false, // Disable file watching to prevent I/O errors
      staticOptions: {
        // Exclude index.html from static serving so webpack can serve its generated version
        ignore: ['**/index.html']
      }
    },
    hot: true,
    allowedHosts: 'all',
    // WebSocket proxy configuration
    // Use /api/ws to avoid conflicts with webpack dev server's WebSocket
    proxy: {
      '/api': {
        target: process.env.API_TARGET || 'http://backend:8000',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug',
        onProxyReq: (_proxyReq, req, _res) => {
          console.log(`🔌 Proxying API request: ${req.method} ${req.url} -> ${process.env.API_TARGET || 'http://backend:8000'}${req.url}`);
        },
        onError: (err, _req, res) => {
          console.error('🔌 API proxy error:', err.message);
          if (res && res.writeHead && typeof res.writeHead === 'function' && !res.headersSent) {
            res.writeHead(503, {
              'Content-Type': 'application/json',
            });
            res.end(JSON.stringify({
              error: 'Backend service unavailable',
              message: 'The backend service is currently unavailable. Please try again later.'
            }));
          }
        }
      },
      '/api/ws': {
        target: process.env.REACT_APP_WS_PROXY_TARGET || 'http://backend:8000',
        changeOrigin: true,
        ws: true, // Enable WebSocket proxying
        secure: false,
        logLevel: 'debug',
        pathRewrite: {
          '^/api/ws': '/ws' // Rewrite /api/ws to /ws for backend
        },
        onProxyReq: (_proxyReq, req, _res) => {
          console.log(`🔌 Proxying WebSocket request: ${req.url} -> ${process.env.REACT_APP_WS_PROXY_TARGET || 'http://backend:8000'}/ws`);
        },
        onError: (err, _req, res) => {
          console.error('🔌 WebSocket proxy error:', err.message);
          // Only try to write response for regular HTTP requests, not WebSocket upgrade requests
          if (res && res.writeHead && typeof res.writeHead === 'function' && !res.headersSent) {
            res.writeHead(503, {
              'Content-Type': 'application/json',
            });
            res.end(JSON.stringify({
              error: 'WebSocket proxy error',
              message: 'The WebSocket service is currently unavailable. Please try again later.'
            }));
          }
        }
      }
    }
  },

  // Plugins
  plugins: [
    new HtmlWebpackPlugin({
      template: './public/index.html',
      filename: 'index.html'
    }),
    new MiniCssExtractPlugin({
      filename: 'static/css/[name].[contenthash:8].css'
    }),
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer']
    }),
    // Define process.env for client-side code
    new webpack.DefinePlugin({
      'process.env': JSON.stringify(process.env)
    }),
    // Explicitly provide process/browser
    new webpack.ProvidePlugin({
      process: 'process/browser'
    }),
    // Bundle analyzer for development and analysis
    ...(process.env.ANALYZE ? [
      new BundleAnalyzerPlugin({
        analyzerMode: 'static',
        reportFilename: 'bundle-report.html',
        openAnalyzer: false,
        generateStatsFile: true,
        statsFilename: 'stats.json'
      })
    ] : [])
  ],

  // External dependencies (loaded from CDN in production for smaller bundles)
  externals: isProduction ? {
    // Large chart libraries can be loaded from CDN
    'recharts': 'Recharts',
    // Quill editor can be loaded from CDN
    'quill': 'Quill',
    // Lodash can be loaded from CDN
    'lodash': '_',
    // Moment.js can be loaded from CDN
    'moment': 'moment'
  } : {},

  // Resolve configuration
  // ULTRA-AGGRESSIVE optimization for sub-1MB bundle
  optimization: {
    splitChunks: {
      chunks: 'all', // Split all chunks aggressively
      minSize: 10000, // Very small minimum chunk size 10KB
      maxSize: 50000, // Very small maximum chunk size 50KB
      maxInitialRequests: 2, // Minimal initial requests
      maxAsyncRequests: 50, // Allow many async chunks
      automaticNameDelimiter: '.',
      cacheGroups: {
        // Only essential React in initial bundle
        react: {
          test: /[\\/]node_modules[\\/](react|react-dom)[\\/]/,
          name: 'react',
          chunks: 'initial',
          priority: 100,
          reuseExistingChunk: true,
          enforce: true,
          maxSize: 150000, // Strict React bundle limit
        },
        // Ant Design - split into smaller async chunks
        antdCore: {
          test: /[\\/]node_modules[\\/]antd[\\/]es[\\/](button|input|form|layout|grid|space|typography|card)[\\/]/,
          name: 'antd-core',
          chunks: 'async',
          priority: 40,
          reuseExistingChunk: true,
          enforce: true,
          maxSize: 150000,
        },
        antdComponents: {
          test: /[\\/]node_modules[\\/]antd[\\/]es[\\/](?!(button|input|form|layout|grid|space|typography|card))/,
          name: 'antd-components',
          chunks: 'async',
          priority: 35,
          reuseExistingChunk: true,
          enforce: true,
          maxSize: 100000,
        },
        // Ant Design icons (lazy loaded)
        antdIcons: {
          test: /[\\/]node_modules[\\/]@ant-design[\\/]icons[\\/]/,
          name: 'antd-icons',
          chunks: 'async',
          priority: 25,
          reuseExistingChunk: true,
          enforce: true,
          maxSize: 100000,
        },
        // Ant Design icons (async only)
        antdIcons: {
          test: /[\\/]node_modules[\\/]@ant-design[\\/]icons[\\/]/,
          name: 'antd-icons',
          chunks: 'async',
          priority: 25,
          reuseExistingChunk: true,
          enforce: true,
          maxSize: 50000, // Very small icon chunks
        },
        // Feature-specific chunks (async only)
        tutorialFeature: {
          test: /[\\/]src[\\/]components[\\/](tutorial|ai|templates|export|collaboration)[\\/]/,
          name: 'features',
          chunks: 'async',
          priority: 22,
          reuseExistingChunk: true,
          minChunks: 1,
          maxSize: 80000, // Smaller feature chunks
        },
        // Other vendor libraries (async only)
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'async',
          priority: 15,
          reuseExistingChunk: true,
          minChunks: 1,
          maxSize: 80000, // Much smaller vendor chunks
        },
        // Application code (async only)
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true,
          chunks: 'async',
          maxSize: 50000, // Very small app chunks
        }
      }
    },
    runtimeChunk: {
      name: 'runtime'
    },
    // Enable module concatenation for better tree shaking
    concatenateModules: true,
    // Enable side effects optimization
    sideEffects: false,
    // Enable usedExports for better tree shaking
    usedExports: true,
    // Minimize initial bundle size
    minimize: isProduction,
    minimizer: isProduction ? [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info', 'console.debug'],
          },
          mangle: {
            safari10: true,
          },
          output: {
            comments: false,
            ascii_only: true,
          },
        },
        extractComments: false,
      }),
    ] : [],
  },

  resolve: {
    extensions: ['.js', '.jsx', '.json'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'process/browser': require.resolve('process/browser')
    },
    fallback: {
      "stream": require.resolve("stream-browserify"),
      "buffer": require.resolve("buffer/"),
      "util": require.resolve("util/"),
      "process": require.resolve("process/browser"),
      "path": require.resolve("path-browserify"),
      "os": require.resolve("os-browserify/browser"),
      "crypto": require.resolve("crypto-browserify"),
      "assert": require.resolve("assert/"),
      "url": require.resolve("url/"),
      "querystring": require.resolve("querystring-es3"),
      "constants": require.resolve("constants-browserify"),
      "vm": require.resolve("vm-browserify"),
      "tty": require.resolve("tty-browserify"),
      "fs": false,
      // Explicitly set these to false to avoid axios Node.js adapter issues
      "http": false,
      "https": false,
      "zlib": false
    }
  },
  target: 'web', // Ensure build targets browsers

  // Performance hints configuration
  performance: {
    hints: isProduction ? 'warning' : false,
    maxEntrypointSize: 300000, // 300KB for entrypoint
    maxAssetSize: 250000, // 250KB for individual assets
    assetFilter: function (assetFilename) {
      // Only show warnings for JS files, not images or other assets
      return assetFilename.endsWith('.js');
    }
  },

  // Stats configuration for bundle analysis
  ...(process.env.ANALYZE && {
    stats: {
      all: false,
      modules: true,
      errors: true,
      warnings: false,
      moduleTrace: true,
      errorDetails: true,
      chunks: true,
      chunkModules: true,
      chunkOrigins: true,
      assets: true,
      assetsSort: 'size',
      reasons: true,
      usedExports: true,
      providedExports: true,
      optimizationBailout: true,
      children: false
    }
  })
};






